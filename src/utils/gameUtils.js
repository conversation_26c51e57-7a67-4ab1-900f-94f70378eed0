// 游戏常量
export const BOARD_SIZE = 15;
export const EMPTY = 0;
export const BLACK = 1;
export const WHITE = 2;

// 创建空棋盘
export const createEmptyBoard = () => {
  return Array(BOARD_SIZE).fill(null).map(() => Array(BOARD_SIZE).fill(EMPTY));
};

// 检查是否有五子连线
export const checkWinner = (board, row, col, player) => {
  const directions = [
    [0, 1],   // 水平
    [1, 0],   // 垂直
    [1, 1],   // 主对角线
    [1, -1]   // 副对角线
  ];

  for (const [dx, dy] of directions) {
    let count = 1; // 包含当前棋子

    // 向一个方向检查
    let r = row + dx;
    let c = col + dy;
    while (r >= 0 && r < BOARD_SIZE && c >= 0 && c < BOARD_SIZE && board[r][c] === player) {
      count++;
      r += dx;
      c += dy;
    }

    // 向相反方向检查
    r = row - dx;
    c = col - dy;
    while (r >= 0 && r < BOARD_SIZE && c >= 0 && c < BOARD_SIZE && board[r][c] === player) {
      count++;
      r -= dx;
      c -= dy;
    }

    if (count >= 5) {
      return true;
    }
  }

  return false;
};

// 检查棋盘是否已满
export const isBoardFull = (board) => {
  for (let i = 0; i < BOARD_SIZE; i++) {
    for (let j = 0; j < BOARD_SIZE; j++) {
      if (board[i][j] === EMPTY) {
        return false;
      }
    }
  }
  return true;
};

// 获取玩家名称
export const getPlayerName = (player) => {
  return player === BLACK ? '黑棋' : '白棋';
};

// 获取下一个玩家
export const getNextPlayer = (currentPlayer) => {
  return currentPlayer === BLACK ? WHITE : BLACK;
};
