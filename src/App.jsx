import React from 'react';
import GameBoard from './components/GameBoard';
import { useGameLogic } from './hooks/useGameLogic';
import { getPlayerName } from './utils/gameUtils';
import './App.css';

function App() {
  const {
    board,
    currentPlayer,
    winner,
    gameOver,
    moveHistory,
    makeMove,
    resetGame,
    undoMove,
    canUndo
  } = useGameLogic();

  const lastMove = moveHistory.length > 0 ? moveHistory[moveHistory.length - 1] : null;

  const getGameStatus = () => {
    if (winner) {
      return `🎉 ${getPlayerName(winner)} 获胜！`;
    }
    if (gameOver) {
      return '🤝 平局！';
    }
    return `轮到 ${getPlayerName(currentPlayer)} 下棋`;
  };

  return (
    <div className="app">
      <div className="game-container">
        <h1 className="game-title">五子棋</h1>
        
        <div className="game-status">
          <h2>{getGameStatus()}</h2>
        </div>

        <div className="game-info">
          <div className="move-count">
            第 {moveHistory.length + 1} 手
          </div>
        </div>

        <GameBoard 
          board={board} 
          onCellClick={makeMove}
          lastMove={lastMove}
        />

        <div className="game-controls">
          <button 
            className="control-button reset-button"
            onClick={resetGame}
          >
            🔄 重新开始
          </button>
          
          <button 
            className="control-button undo-button"
            onClick={undoMove}
            disabled={!canUndo}
          >
            ↶ 悔棋
          </button>
        </div>

        <div className="game-rules">
          <h3>游戏规则</h3>
          <ul>
            <li>黑棋先手，白棋后手</li>
            <li>在棋盘上轮流下棋</li>
            <li>率先形成五子连线者获胜</li>
            <li>连线可以是横、竖、斜任意方向</li>
          </ul>
        </div>
      </div>
    </div>
  );
}

export default App;
