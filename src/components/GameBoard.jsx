import React from 'react';
import Cell from './Cell';
import { BOARD_SIZE } from '../utils/gameUtils';

const GameBoard = ({ board, onCellClick, lastMove }) => {
  const isLastMove = (row, col) => {
    return lastMove && lastMove.row === row && lastMove.col === col;
  };

  return (
    <div 
      className="game-board"
      style={{
        display: 'grid',
        gridTemplateColumns: `repeat(${BOARD_SIZE}, 30px)`,
        gridTemplateRows: `repeat(${BOARD_SIZE}, 30px)`,
        gap: '0',
        border: '2px solid #8B4513',
        backgroundColor: '#DEB887',
        padding: '10px',
        borderRadius: '8px',
        boxShadow: '0 4px 8px rgba(0, 0, 0, 0.2)'
      }}
    >
      {board.map((row, rowIndex) =>
        row.map((cell, colIndex) => (
          <Cell
            key={`${rowIndex}-${colIndex}`}
            value={cell}
            onClick={onCellClick}
            row={rowIndex}
            col={colIndex}
            isLastMove={isLastMove(rowIndex, colIndex)}
          />
        ))
      )}
    </div>
  );
};

export default GameBoard;
