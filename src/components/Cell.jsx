import React from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, WHITE, EMPTY } from '../utils/gameUtils';

const Cell = ({ value, onClick, row, col, isLastMove }) => {
  const getCellContent = () => {
    if (value === EMPTY) return null;
    
    return (
      <div 
        className={`piece ${value === BLACK ? 'black' : 'white'} ${isLastMove ? 'last-move' : ''}`}
      />
    );
  };

  return (
    <div 
      className="cell" 
      onClick={() => onClick(row, col)}
      style={{
        position: 'relative',
        width: '30px',
        height: '30px',
        border: '1px solid #8B4513',
        backgroundColor: '#DEB887',
        cursor: value === EMPTY ? 'pointer' : 'default',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      }}
    >
      {getCellContent()}
    </div>
  );
};

export default Cell;
