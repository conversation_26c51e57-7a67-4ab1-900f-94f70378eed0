import { useState, useCallback } from 'react';
import {
  createEmptyBoard,
  checkWinner,
  isBoardFull,
  getNextPlayer,
  BLACK,
  WHITE,
  EMPTY
} from '../utils/gameUtils';

export const useGameLogic = () => {
  const [board, setBoard] = useState(createEmptyBoard);
  const [currentPlayer, setCurrentPlayer] = useState(BLACK);
  const [winner, setWinner] = useState(null);
  const [gameOver, setGameOver] = useState(false);
  const [moveHistory, setMoveHistory] = useState([]);

  // 下棋
  const makeMove = useCallback((row, col) => {
    // 检查游戏是否结束或位置是否已被占用
    if (gameOver || board[row][col] !== EMPTY) {
      return false;
    }

    // 创建新棋盘
    const newBoard = board.map(row => [...row]);
    newBoard[row][col] = currentPlayer;

    // 更新棋盘
    setBoard(newBoard);

    // 记录移动历史
    setMoveHistory(prev => [...prev, { row, col, player: currentPlayer }]);

    // 检查是否获胜
    if (checkWinner(newBoard, row, col, currentPlayer)) {
      setWinner(currentPlayer);
      setGameOver(true);
      return true;
    }

    // 检查是否平局
    if (isBoardFull(newBoard)) {
      setGameOver(true);
      return true;
    }

    // 切换玩家
    setCurrentPlayer(getNextPlayer(currentPlayer));
    return true;
  }, [board, currentPlayer, gameOver]);

  // 重新开始游戏
  const resetGame = useCallback(() => {
    setBoard(createEmptyBoard());
    setCurrentPlayer(BLACK);
    setWinner(null);
    setGameOver(false);
    setMoveHistory([]);
  }, []);

  // 悔棋
  const undoMove = useCallback(() => {
    if (moveHistory.length === 0 || gameOver) {
      return false;
    }

    const newHistory = [...moveHistory];
    const lastMove = newHistory.pop();
    
    const newBoard = board.map(row => [...row]);
    newBoard[lastMove.row][lastMove.col] = EMPTY;

    setBoard(newBoard);
    setMoveHistory(newHistory);
    setCurrentPlayer(lastMove.player);
    setWinner(null);
    setGameOver(false);

    return true;
  }, [board, moveHistory, gameOver]);

  return {
    board,
    currentPlayer,
    winner,
    gameOver,
    moveHistory,
    makeMove,
    resetGame,
    undoMove,
    canUndo: moveHistory.length > 0 && !gameOver
  };
};
