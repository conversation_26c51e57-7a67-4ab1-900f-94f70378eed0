.app {
  text-align: center;
  padding: 20px;
}

.game-container {
  max-width: 600px;
  margin: 0 auto;
  background: white;
  border-radius: 12px;
  padding: 30px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
}

.game-title {
  color: #2c3e50;
  margin-bottom: 20px;
  font-size: 2.5rem;
  font-weight: bold;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.game-status {
  margin-bottom: 15px;
}

.game-status h2 {
  color: #34495e;
  font-size: 1.5rem;
  margin: 0;
  padding: 10px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border-radius: 8px;
  border: 2px solid #bdc3c7;
}

.game-info {
  margin-bottom: 20px;
}

.move-count {
  color: #7f8c8d;
  font-size: 1.1rem;
  font-weight: 500;
}

.game-board {
  margin: 20px auto;
  display: inline-block;
}

/* 棋子样式 */
.piece {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  border: 2px solid #2c3e50;
  position: relative;
  transition: all 0.2s ease;
}

.piece.black {
  background: radial-gradient(circle at 30% 30%, #555, #000);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.piece.white {
  background: radial-gradient(circle at 30% 30%, #fff, #ddd);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.piece.last-move {
  animation: pulse 1s ease-in-out;
  box-shadow: 0 0 10px rgba(255, 215, 0, 0.8);
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

.cell:hover {
  background-color: #F4A460 !important;
  transition: background-color 0.2s ease;
}

.game-controls {
  margin: 30px 0;
  display: flex;
  gap: 15px;
  justify-content: center;
}

.control-button {
  padding: 12px 24px;
  font-size: 1.1rem;
  font-weight: 600;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 120px;
}

.reset-button {
  background: linear-gradient(135deg, #e74c3c, #c0392b);
  color: white;
}

.reset-button:hover {
  background: linear-gradient(135deg, #c0392b, #a93226);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(231, 76, 60, 0.3);
}

.undo-button {
  background: linear-gradient(135deg, #3498db, #2980b9);
  color: white;
}

.undo-button:hover:not(:disabled) {
  background: linear-gradient(135deg, #2980b9, #21618c);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
}

.undo-button:disabled {
  background: #bdc3c7;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.game-rules {
  margin-top: 30px;
  text-align: left;
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  border-left: 4px solid #3498db;
}

.game-rules h3 {
  color: #2c3e50;
  margin-bottom: 15px;
  font-size: 1.3rem;
}

.game-rules ul {
  color: #34495e;
  line-height: 1.6;
}

.game-rules li {
  margin-bottom: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .game-container {
    padding: 20px;
    margin: 10px;
  }
  
  .game-title {
    font-size: 2rem;
  }
  
  .game-board {
    transform: scale(0.8);
  }
  
  .game-controls {
    flex-direction: column;
    align-items: center;
  }
  
  .control-button {
    width: 200px;
  }
}
